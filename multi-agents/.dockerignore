# Git相关
.git
.gitignore
.gitattributes

# Docker相关
Dockerfile
docker-compose*.yml
.dockerignore

# 环境变量
.env
.env.local
.env.*.local

# 依赖目录
node_modules
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# IDE和编辑器
.vscode
.idea
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 构建产物
dist
build
*.egg-info

# 日志文件
*.log
logs

# 临时文件
tmp
temp
*.tmp
*.temp

# 文档
README.md
docs/
*.md

# 测试文件
tests/
test/
*.test.js
*.spec.js
