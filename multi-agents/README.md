# 多功能AI演示系统

这是一个基于现代技术栈构建的多功能AI演示系统，包含文章评分和数学解题功能。支持传统部署和Docker容器化部署。

## 🚀 快速开始

### Docker 部署（推荐）

1. **环境准备**
   ```bash
   # 确保已安装 Docker 和 Docker Compose
   docker --version
   docker-compose --version
   ```

2. **配置环境变量**
   ```bash
   # 复制环境变量示例文件
   cp .env.example .env

   # 编辑环境变量（必须设置 OPENAI_API_KEY）
   vim .env
   ```

3. **启动服务**
   ```bash
   # 方式1: 使用 Makefile（推荐）
   make dev          # 开发环境
   make prod         # 生产环境

   # 方式2: 使用部署脚本
   ./deploy.sh dev   # 开发环境
   ./deploy.sh prod  # 生产环境

   # 方式3: 使用 Docker Compose
   docker-compose -f docker-compose.dev.yml up -d   # 开发环境
   docker-compose -f docker-compose.prod.yml up -d  # 生产环境
   ```

4. **访问应用**
   - **开发环境**：
     - 前端：http://localhost:3000
     - 后端API：http://localhost:8000
     - API文档：http://localhost:8000/docs

   - **生产环境**：
     - 应用：http://localhost
     - API：http://localhost/api/
     - 健康检查：http://localhost/health

### 传统部署

详见 [webui/README.md](webui/README.md) 中的开发指南。

## 📁 项目结构

```
multi-agents/
├── demo/                          # 后端服务
│   ├── app.py                    # 统一API服务器
│   ├── article/                  # 文章评分模块
│   ├── mathsolver/               # 数学解题模块
│   ├── config.py                 # 配置管理
│   └── Dockerfile               # 后端Docker配置
├── webui/                        # 前端服务
│   ├── src/                     # React源码
│   ├── package.json             # 前端依赖
│   └── Dockerfile               # 前端Docker配置
├── docker-compose.yml            # 基础Docker配置
├── docker-compose.dev.yml        # 开发环境配置
├── docker-compose.prod.yml       # 生产环境配置
├── nginx.conf                    # Nginx反向代理配置
├── deploy.sh                     # 部署脚本
├── Makefile                      # 便捷命令
└── DOCKER_DEPLOYMENT.md          # 详细部署文档
```

## 🛠️ 常用命令

### Makefile 命令

```bash
make help         # 显示帮助信息
make dev          # 启动开发环境
make prod         # 启动生产环境
make logs         # 查看日志
make status       # 查看服务状态
make health       # 检查服务健康状态
make clean        # 清理Docker资源
```

### 部署脚本命令

```bash
./deploy.sh dev           # 启动开发环境
./deploy.sh prod          # 启动生产环境
./deploy.sh stop [env]    # 停止服务
./deploy.sh logs [env]    # 查看日志
./deploy.sh build [env]   # 构建镜像
```

## 🔧 配置说明

### 必需环境变量

| 变量名          | 说明              | 示例                      |
| --------------- | ----------------- | ------------------------- |
| OPENAI_API_KEY  | OpenAI API密钥    | sk-xxx...                 |
| OPENAI_BASE_URL | OpenAI API基础URL | https://api.openai.com/v1 |

### 可选环境变量

| 变量名              | 说明            | 默认值 |
| ------------------- | --------------- | ------ |
| LANGFUSE_PUBLIC_KEY | Langfuse公钥    | -      |
| LANGFUSE_SECRET_KEY | Langfuse私钥    | -      |
| LANGFUSE_BASE_URL   | Langfuse基础URL | -      |

## 📚 详细文档

- [Docker部署指南](DOCKER_DEPLOYMENT.md) - 完整的Docker部署文档
- [前端开发指南](webui/README.md) - 前端开发和传统部署
- [API文档](http://localhost:8000/docs) - 后端API接口文档

## 🎯 功能特色

### 📝 文章评分
- 实时流式评分过程
- 多维度分析结果
- 智能反馈建议

### 🧮 数学解题
- 步骤化解题过程
- LaTeX公式渲染
- 详细解题讲解

## 🔍 故障排除

1. **端口冲突**：修改 docker-compose.yml 中的端口映射
2. **环境变量问题**：检查 .env 文件是否正确配置
3. **网络问题**：使用 `make clean` 清理并重新启动
4. **权限问题**：确保 deploy.sh 有执行权限

更多故障排除信息请参考 [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)。

## 📄 许可证

本项目采用 MIT 许可证。