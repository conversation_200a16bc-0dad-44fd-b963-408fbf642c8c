#!/usr/bin/env python3
"""
统一的多功能AI服务器
整合作文评分和数学解题功能
"""

import os
import sys
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import uvicorn
from article.models import ArticleRequest
from article.service import process_article_scoring
from config import SSE_HEADERS, settings
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from mathsolver.models import MathRequest
from mathsolver.service import process_math_solving
from models import APIInfo, HealthCheck

# 创建FastAPI应用
app = FastAPI(
    title="统一AI服务API",
    description="整合作文评分和数学解题功能的统一服务",
    version="1.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.CORS_METHODS,
    allow_headers=settings.CORS_HEADERS,
)


@app.get("/", response_model=APIInfo)
async def root():
    """API根路径，返回服务信息"""
    return APIInfo(
        message="统一AI服务API运行中",
        description="整合作文评分和数学解题功能的统一服务",
        version="1.0.0",
    )


@app.get("/health", response_model=HealthCheck)
async def health_check():
    """健康检查端点"""
    return HealthCheck(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        services={
            "essay_scoring": "available",
            "math_solving": "available",
            "llm_service": "available",
        },
    )


@app.post("/api/v1/article/review")
async def score_essay(request: ArticleRequest):
    """文章评分SSE端点"""
    if not request.article.strip():
        raise HTTPException(status_code=400, detail="文章内容不能为空")

    return StreamingResponse(
        process_article_scoring(request.article),
        media_type="text/event-stream",
        headers=SSE_HEADERS,
    )


@app.post("/api/v1/math/solve")
async def solve_math(request: MathRequest):
    """数学解题SSE端点"""
    if not request.problem.strip():
        raise HTTPException(status_code=400, detail="题目内容不能为空")

    return StreamingResponse(
        process_math_solving(request.problem),
        media_type="text/event-stream",
        headers=SSE_HEADERS,
    )


if __name__ == "__main__":
    print("🚀 启动统一AI服务...")
    print(f"📱 访问地址: http://localhost:{settings.PORT}")
    print("📚 API文档: http://localhost:{}/docs".format(settings.PORT))
    print("🛑 按 Ctrl+C 停止服务")

    uvicorn.run(app, host=settings.HOST, port=settings.PORT, reload=False)
