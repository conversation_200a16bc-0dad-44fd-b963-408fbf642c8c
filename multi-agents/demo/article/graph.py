#!/usr/bin/env python3
"""
文章评分的LangGraph图构建
"""

from langgraph.constants import E<PERSON>, START
from langgraph.graph import StateGraph

from article.models import ArticleState
from article.nodes import (
    content_analysis_node,
    final_scoring_node,
    language_analysis_node,
    structure_analysis_node,
    summary_node,
    topic_analysis_node,
    word_count_node,
)


def create_article_scoring_graph():
    """创建文章评分流程图"""

    graph = (
        StateGraph(ArticleState)
        .add_node("word_count_node", word_count_node)
        .add_node("topic_analysis_node", topic_analysis_node)
        .add_node("structure_analysis_node", structure_analysis_node)
        .add_node("content_analysis_node", content_analysis_node)
        .add_node("language_analysis_node", language_analysis_node)
        .add_node("final_scoring_node", final_scoring_node)
        .add_node("summary_node", summary_node)
        .add_edge(START, "word_count_node")
        .add_edge("word_count_node", "topic_analysis_node")
        .add_edge("topic_analysis_node", "structure_analysis_node")
        .add_edge("structure_analysis_node", "content_analysis_node")
        .add_edge("content_analysis_node", "language_analysis_node")
        .add_edge("language_analysis_node", "final_scoring_node")
        .add_edge("final_scoring_node", "summary_node")
        .add_edge("summary_node", END)
        .compile(name="文章评分Graph")
    )

    return graph


# 创建全局图实例
article_scoring_graph = create_article_scoring_graph()
