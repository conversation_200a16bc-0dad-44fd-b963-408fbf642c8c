#!/usr/bin/env python3
"""
文章评分相关的数据模型
"""

from typing import Any, TypedDict

from pydantic import BaseModel, Field


# Pydantic模型 - 用于结构化输出
class TopicAnalysis(BaseModel):
    """主题分析结果"""

    theme_clarity_score: int = Field(description="主题明确性得分(0-4)", ge=0, le=4)
    depth_score: int = Field(description="立意深度得分(0-4)", ge=0, le=4)
    relevance_score: int = Field(description="主题相关性得分(0-4)", ge=0, le=4)
    values_score: int = Field(description="价值观导向得分(0-3)", ge=0, le=3)
    feedback: str = Field(description="具体评价和建议")


class StructureAnalysis(BaseModel):
    """结构分析结果"""

    opening_score: int = Field(description="开头效果得分(0-3)", ge=0, le=3)
    paragraph_score: int = Field(description="段落层次得分(0-3)", ge=0, le=3)
    transition_score: int = Field(description="过渡衔接得分(0-2)", ge=0, le=2)
    ending_score: int = Field(description="结尾效果得分(0-2)", ge=0, le=2)
    feedback: str = Field(description="具体评价和建议")


class ContentAnalysis(BaseModel):
    """内容分析结果"""

    material_score: int = Field(description="材料丰富性得分(0-4)", ge=0, le=4)
    logic_score: int = Field(description="论证逻辑得分(0-4)", ge=0, le=4)
    innovation_score: int = Field(description="创新性得分(0-4)", ge=0, le=4)
    depth_score: int = Field(description="深刻性得分(0-3)", ge=0, le=3)
    feedback: str = Field(description="具体评价和建议")


class LanguageAnalysis(BaseModel):
    """语言分析结果"""

    accuracy_score: int = Field(description="准确性得分(0-4)", ge=0, le=4)
    fluency_score: int = Field(description="流畅性得分(0-4)", ge=0, le=4)
    vividness_score: int = Field(description="生动性得分(0-4)", ge=0, le=4)
    standard_score: int = Field(description="规范性得分(0-3)", ge=0, le=3)
    feedback: str = Field(description="具体评价和建议")


# TypedDict状态模型 - 用于LangGraph状态管理
class ArticleState(TypedDict):
    """文章评分状态"""

    article: str
    word_count: int
    topic_analysis: dict[str, Any]
    structure_analysis: dict[str, Any]
    content_analysis: dict[str, Any]
    language_analysis: dict[str, Any]
    deduction_details: dict[str, Any]
    final_score: dict[str, Any]
    summary: str
    progress_info: dict[str, Any]


# API请求模型
class ArticleRequest(BaseModel):
    """文章评分请求模型"""

    article: str


# 评分结果模型
class ArticleScoreResult(BaseModel):
    """文章评分结果"""

    word_count: int
    word_score: int
    topic_score: int
    structure_score: int
    content_score: int
    language_score: int
    total_score: int
    max_score: int = 60
    topic_analysis: dict
    structure_analysis: dict
    content_analysis: dict
    language_analysis: dict
    summary: str
    deduction_details: dict
