#!/usr/bin/env python3
"""
文章评分的节点函数
"""

import os
import re
import sys

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import get_llm
from langgraph.config import get_stream_writer

from article.models import (
    ArticleState,
    ContentAnalysis,
    LanguageAnalysis,
    StructureAnalysis,
    TopicAnalysis,
)
from article.prompts import (
    get_content_analysis_messages,
    get_language_analysis_messages,
    get_structure_analysis_messages,
    get_summary_generation_messages,
    get_topic_analysis_messages,
)


async def word_count_node(state: ArticleState):
    """字数统计节点"""
    writer = get_stream_writer()

    writer({"type": "progress", "step": "word_count", "message": "正在统计字数..."})

    article = state["article"]

    # 移除标点符号和空格，只统计汉字、字母和数字
    clean_text = re.sub(r"[^\u4e00-\u9fa5a-zA-Z0-9]", "", article)
    word_count = len(clean_text)

    # 字数评分逻辑（满分5分）
    if word_count >= 800:
        score = 5
    elif word_count >= 600:
        score = 4
    elif word_count >= 400:
        score = 3
    elif word_count >= 200:
        score = 2
    else:
        score = 1

    writer(
        {
            "type": "result",
            "step": "word_count",
            "word_count": word_count,
            "score": score,
        }
    )

    return {
        "word_count": word_count,
        "progress_info": {"word_count": {"count": word_count, "score": score}},
    }


async def topic_analysis_node(state: ArticleState):
    """主题分析节点"""
    writer = get_stream_writer()

    writer(
        {"type": "progress", "step": "topic_analysis", "message": "正在分析文章主题..."}
    )

    article = state["article"]
    llm = get_llm().with_structured_output(TopicAnalysis)

    messages = get_topic_analysis_messages(article)
    analysis_result = await llm.ainvoke(messages)

    total_score = (
        analysis_result.theme_clarity_score
        + analysis_result.depth_score
        + analysis_result.relevance_score
        + analysis_result.values_score
    )

    writer(
        {
            "type": "result",
            "step": "topic_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"topic_analysis": analysis_result.model_dump()}


async def structure_analysis_node(state: ArticleState):
    """结构分析节点"""
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "structure_analysis",
            "message": "正在分析文章结构...",
        }
    )

    article = state["article"]
    llm = get_llm().with_structured_output(StructureAnalysis)

    messages = get_structure_analysis_messages(article)
    analysis_result = await llm.ainvoke(messages)

    total_score = (
        analysis_result.opening_score
        + analysis_result.paragraph_score
        + analysis_result.transition_score
        + analysis_result.ending_score
    )

    writer(
        {
            "type": "result",
            "step": "structure_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"structure_analysis": analysis_result.model_dump()}


async def content_analysis_node(state: ArticleState):
    """内容分析节点"""
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "content_analysis",
            "message": "正在分析文章内容...",
        }
    )

    article = state["article"]
    llm = get_llm().with_structured_output(ContentAnalysis)

    messages = get_content_analysis_messages(article)
    analysis_result = await llm.ainvoke(messages)

    total_score = (
        analysis_result.material_score
        + analysis_result.logic_score
        + analysis_result.innovation_score
        + analysis_result.depth_score
    )

    writer(
        {
            "type": "result",
            "step": "content_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"content_analysis": analysis_result.model_dump()}


async def language_analysis_node(state: ArticleState):
    """语言分析节点"""
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "language_analysis",
            "message": "正在分析语言表达...",
        }
    )

    article = state["article"]
    llm = get_llm().with_structured_output(LanguageAnalysis)

    messages = get_language_analysis_messages(article)
    analysis_result = await llm.ainvoke(messages)

    total_score = (
        analysis_result.accuracy_score
        + analysis_result.fluency_score
        + analysis_result.vividness_score
        + analysis_result.standard_score
    )

    writer(
        {
            "type": "result",
            "step": "language_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"language_analysis": analysis_result.model_dump()}


async def final_scoring_node(state: ArticleState):
    """最终评分节点"""
    writer = get_stream_writer()

    writer(
        {"type": "progress", "step": "final_scoring", "message": "正在计算最终得分..."}
    )

    # 获取各项分析结果
    word_count_info = state.get("progress_info", {}).get("word_count", {})
    topic_analysis = state.get("topic_analysis", {})
    structure_analysis = state.get("structure_analysis", {})
    content_analysis = state.get("content_analysis", {})
    language_analysis = state.get("language_analysis", {})

    # 计算各维度得分
    word_score = word_count_info.get("score", 0)

    topic_score = (
        topic_analysis.get("theme_clarity_score", 0)
        + topic_analysis.get("depth_score", 0)
        + topic_analysis.get("relevance_score", 0)
        + topic_analysis.get("values_score", 0)
    )

    structure_score = (
        structure_analysis.get("opening_score", 0)
        + structure_analysis.get("paragraph_score", 0)
        + structure_analysis.get("transition_score", 0)
        + structure_analysis.get("ending_score", 0)
    )

    content_score = (
        content_analysis.get("material_score", 0)
        + content_analysis.get("logic_score", 0)
        + content_analysis.get("innovation_score", 0)
        + content_analysis.get("depth_score", 0)
    )

    language_score = (
        language_analysis.get("accuracy_score", 0)
        + language_analysis.get("fluency_score", 0)
        + language_analysis.get("vividness_score", 0)
        + language_analysis.get("standard_score", 0)
    )

    # 计算总分（满分60分）
    total_score = (
        word_score + topic_score + structure_score + content_score + language_score
    )

    # 扣分项检查
    deduction_details = {}

    # 检查字数不足扣分
    word_count = state.get("word_count", 0)
    if word_count < 200:
        deduction_details["字数不足"] = f"字数仅{word_count}字，建议至少400字"

    final_score_details = {
        "word_score": word_score,
        "topic_score": topic_score,
        "structure_score": structure_score,
        "content_score": content_score,
        "language_score": language_score,
        "total_score": total_score,
        "max_score": 60,
        "score_breakdown": {
            "字数标点(5分)": word_score,
            "主题立意(15分)": topic_score,
            "结构层次(10分)": structure_score,
            "内容表达(15分)": content_score,
            "语言文字(15分)": language_score,
        },
    }

    writer({"type": "result", "step": "final_scoring", "details": final_score_details})

    return {"final_score": final_score_details, "deduction_details": deduction_details}


async def summary_node(state: ArticleState):
    """总结生成节点"""
    writer = get_stream_writer()

    writer({"type": "progress", "step": "summary", "message": "正在生成评价总结..."})

    # 获取评分信息
    final_score = state.get("final_score", {})
    word_count = state.get("word_count", 0)

    word_score = final_score.get("word_score", 0)
    topic_score = final_score.get("topic_score", 0)
    structure_score = final_score.get("structure_score", 0)
    content_score = final_score.get("content_score", 0)
    language_score = final_score.get("language_score", 0)
    total_score = final_score.get("total_score", 0)

    llm = get_llm()
    messages = get_summary_generation_messages(
        word_count,
        word_score,
        topic_score,
        structure_score,
        content_score,
        language_score,
        total_score,
    )

    summary = await llm.ainvoke(messages)
    summary_text = summary.content

    writer({"type": "result", "step": "summary", "summary": summary_text})

    return {"summary": summary_text}
