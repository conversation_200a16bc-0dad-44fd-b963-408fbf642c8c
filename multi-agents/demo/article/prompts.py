#!/usr/bin/env python3
"""
文章评分相关的提示词模板
"""

# 主题分析提示词
TOPIC_ANALYSIS_PROMPT = """你是一位专业的语文老师，擅长分析文章的主题和立意。

请仔细阅读以下文章，从主题和立意的角度进行深入分析。

请从以下四个维度对文章进行评价：
1. 主题明确性（是否有明确的中心思想）- 评分范围0-4分
2. 立意深度（思想深度和见解独特性）- 评分范围0-4分
3. 主题相关性（内容是否围绕主题展开）- 评分范围0-4分
4. 价值观导向（是否传递正确的价值观）- 评分范围0-3分

请客观公正地评分，并提供具体的评价和改进建议。"""


# 结构分析提示词
STRUCTURE_ANALYSIS_PROMPT = """你是一位专业的语文老师，擅长分析文章的结构和组织。

请仔细阅读以下文章，从结构组织的角度进行深入分析。

请从以下四个维度对文章结构进行评价：
1. 开头效果（是否引人入胜，点题明确）- 评分范围0-3分
2. 段落层次（段落划分是否合理，层次是否清晰）- 评分范围0-3分
3. 过渡衔接（段落间的过渡是否自然）- 评分范围0-2分
4. 结尾效果（是否呼应开头，总结有力）- 评分范围0-2分

请客观公正地评分，并提供具体的评价和改进建议。"""


# 内容分析提示词
CONTENT_ANALYSIS_PROMPT = """你是一位专业的语文老师，擅长分析文章的内容质量。

请仔细阅读以下文章，从内容质量的角度进行深入分析。

请从以下四个维度对文章内容进行评价：
1. 材料丰富性（事例、论据是否充分）- 评分范围0-4分
2. 论证逻辑（推理是否严密，逻辑是否清晰）- 评分范围0-4分
3. 创新性（观点是否新颖，表达是否独特）- 评分范围0-4分
4. 深刻性（分析是否深入，见解是否深刻）- 评分范围0-3分

请客观公正地评分，并提供具体的评价和建议。"""


# 语言分析提示词
LANGUAGE_ANALYSIS_PROMPT = """你是一位专业的语文老师，擅长分析文章的语言表达。

请仔细阅读以下文章，从语言表达的角度进行深入分析。

请从以下四个维度对文章语言进行评价：
1. 准确性（用词是否准确，语法是否正确）- 评分范围0-4分
2. 流畅性（语句是否通顺，表达是否流畅）- 评分范围0-4分
3. 生动性（语言是否生动，是否有感染力）- 评分范围0-4分
4. 规范性（是否符合语言规范，格式是否正确）- 评分范围0-3分

请客观公正地评分，并提供具体的评价和建议。"""


# 总结生成提示词
SUMMARY_GENERATION_PROMPT = """你是一位专业的语文老师，擅长对作文进行综合评价。

基于以下评分结果，请生成一份综合性的作文评价总结：

评分详情：
- 字数标点：{word_count}字（得分：{word_score}/5分）
- 主题立意：{topic_score}/15分
- 结构层次：{structure_score}/10分
- 内容表达：{content_score}/15分
- 语言文字：{language_score}/15分
- 总分：{total_score}/60分

请生成一份包含以下内容的综合评价：
1. 总体评价（2-3句话概括文章的整体水平）
2. 主要优点（列出2-3个突出的优点）
3. 需要改进的地方（列出2-3个具体的改进建议）
4. 鼓励性建议（给出积极正面的建议和鼓励）

请用温和、专业、鼓励的语调，帮助学生更好地理解自己的作文水平并获得改进方向。"""


def get_topic_analysis_messages(article: str):
    """获取主题分析的消息"""
    from langchain.schema import HumanMessage, SystemMessage

    return [
        SystemMessage(content=TOPIC_ANALYSIS_PROMPT),
        HumanMessage(content=f"请分析以下文章的主题和立意：\n\n{article}"),
    ]


def get_structure_analysis_messages(article: str):
    """获取结构分析的消息"""
    from langchain.schema import HumanMessage, SystemMessage

    return [
        SystemMessage(content=STRUCTURE_ANALYSIS_PROMPT),
        HumanMessage(content=f"请分析以下文章的结构：\n\n{article}"),
    ]


def get_content_analysis_messages(article: str):
    """获取内容分析的消息"""
    from langchain.schema import HumanMessage, SystemMessage

    return [
        SystemMessage(content=CONTENT_ANALYSIS_PROMPT),
        HumanMessage(content=f"请分析以下文章的内容质量：\n\n{article}"),
    ]


def get_language_analysis_messages(article: str):
    """获取语言分析的消息"""
    from langchain.schema import HumanMessage, SystemMessage

    return [
        SystemMessage(content=LANGUAGE_ANALYSIS_PROMPT),
        HumanMessage(content=f"请分析以下文章的语言表达：\n\n{article}"),
    ]


def get_summary_generation_messages(
    word_count: int,
    word_score: int,
    topic_score: int,
    structure_score: int,
    content_score: int,
    language_score: int,
    total_score: int,
):
    """获取总结生成的消息"""
    from langchain.schema import HumanMessage, SystemMessage

    prompt = SUMMARY_GENERATION_PROMPT.format(
        word_count=word_count,
        word_score=word_score,
        topic_score=topic_score,
        structure_score=structure_score,
        content_score=content_score,
        language_score=language_score,
        total_score=total_score,
    )

    return [
        SystemMessage(content=prompt),
        HumanMessage(content="请基于以上信息生成综合评价总结。"),
    ]
