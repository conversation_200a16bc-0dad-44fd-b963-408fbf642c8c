#!/usr/bin/env python3
"""
文章评分服务
"""

import json
from typing import AsyncGenerator

from config import get_langfuse_handler
from sse_utils import create_complete_event, create_error_event, create_start_event

# 导入文章评分的graph
try:
    from article import article_scoring_graph as graph

    print("✅ 成功导入文章评分graph")
except ImportError as e:
    print(f"❌ 导入文章评分graph失败: {e}")
    graph = None


async def process_article_scoring(article: str) -> AsyncGenerator[str, None]:
    """处理文章评分并通过SSE流式返回结果"""

    if not article.strip():
        yield create_error_event("文章内容不能为空")
        return

    if graph is None:
        yield create_error_event("作文评分服务暂不可用")
        return

    try:
        # 发送开始信号
        yield create_start_event("开始作文评分...")

        initial_state = {"article": article.strip()}
        langfuse_handler = get_langfuse_handler()

        # 用于收集最终结果
        final_result = {
            "wordCount": 0,
            "topicAnalysis": None,
            "structureAnalysis": None,
            "contentAnalysis": None,
            "languageAnalysis": None,
            "finalScore": None,
            "summary": None,
        }

        # 运行评分流程
        async for mode, data in graph.astream(
            initial_state,
            stream_mode=["updates", "custom"],
            config={
                "configurable": {"thread_id": "essay_scoring"},
                "callbacks": [langfuse_handler],
            },
        ):
            if mode == "custom":
                if data.get("type") == "progress":
                    step = data.get("step", "")
                    message = data.get("message", "")
                    yield f"data: {json.dumps({'type': 'progress', 'step': step, 'message': message}, ensure_ascii=False)}\n\n"

                elif data.get("type") == "result":
                    step = data.get("step", "")

                    if step == "word_count":
                        final_result["wordCount"] = data.get("word_count", 0)
                        yield f"data: {json.dumps({'type': 'result', 'step': 'word_count', 'data': {'count': data.get('word_count', 0), 'score': data.get('score', 0)}}, ensure_ascii=False)}\n\n"

                    elif step == "topic_analysis":
                        final_result["topicAnalysis"] = data.get("details", {})
                        yield f"data: {json.dumps({'type': 'result', 'step': 'topic_analysis', 'data': {'score': data.get('score', 0), 'details': data.get('details', {})}}, ensure_ascii=False)}\n\n"

                    elif step == "structure_analysis":
                        final_result["structureAnalysis"] = data.get("details", {})
                        yield f"data: {json.dumps({'type': 'result', 'step': 'structure_analysis', 'data': {'score': data.get('score', 0), 'details': data.get('details', {})}}, ensure_ascii=False)}\n\n"

                    elif step == "content_analysis":
                        final_result["contentAnalysis"] = data.get("details", {})
                        yield f"data: {json.dumps({'type': 'result', 'step': 'content_analysis', 'data': {'score': data.get('score', 0), 'details': data.get('details', {})}}, ensure_ascii=False)}\n\n"

                    elif step == "language_analysis":
                        final_result["languageAnalysis"] = data.get("details", {})
                        yield f"data: {json.dumps({'type': 'result', 'step': 'language_analysis', 'data': {'score': data.get('score', 0), 'details': data.get('details', {})}}, ensure_ascii=False)}\n\n"

                    elif step == "final_scoring":
                        final_result["finalScore"] = data.get("details", {})
                        yield f"data: {json.dumps({'type': 'result', 'step': 'final_scoring', 'data': data.get('details', {})}, ensure_ascii=False)}\n\n"

                    elif step == "summary":
                        final_result["summary"] = data.get("summary", "")
                        yield f"data: {json.dumps({'type': 'result', 'step': 'summary', 'data': {'summary': data.get('summary', '')}}, ensure_ascii=False)}\n\n"

        # 发送最终完整结果
        yield f"data: {json.dumps({'type': 'final_result', 'data': final_result}, ensure_ascii=False)}\n\n"

        # 发送完成信号
        yield create_complete_event("作文评分完成！")

    except Exception as e:
        yield create_error_event(f"作文评分过程中出现错误: {str(e)}")
