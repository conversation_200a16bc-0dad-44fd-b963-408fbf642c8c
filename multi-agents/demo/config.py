#!/usr/bin/env python3
"""
统一配置模块
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langfuse.callback import CallbackHandler

# 加载环境变量
load_dotenv()


class Settings:
    """应用配置"""
    
    # OpenAI配置
    OPENAI_MODEL: str = "gpt-4o"
    OPENAI_BASE_URL: str = os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1")
    OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY")
    OPENAI_TEMPERATURE: float = 0.3
    
    # Langfuse配置
    LANGFUSE_PUBLIC_KEY: str = os.getenv("LANGFUSE_PUBLIC_KEY", "pk-lf-fae0c7b5-9678-4955-beb7-8ede32ca9548")
    LANGFUSE_SECRET_KEY: str = os.getenv("LANGFUSE_SECRET_KEY", "******************************************")
    LANGFUSE_HOST: str = os.getenv("LANGFUSE_BASE_URL", "https://langfuse.xiaoxingcloud.com")
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # CORS配置
    CORS_ORIGINS: list = ["*"]
    CORS_METHODS: list = ["*"]
    CORS_HEADERS: list = ["*"]


# 全局配置实例
settings = Settings()


def get_llm() -> ChatOpenAI:
    """获取配置好的LLM实例"""
    return ChatOpenAI(
        model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
        api_key=settings.OPENAI_API_KEY,
        temperature=settings.OPENAI_TEMPERATURE,
    )


def get_langfuse_handler() -> CallbackHandler:
    """获取配置好的Langfuse回调处理器"""
    return CallbackHandler(
        public_key=settings.LANGFUSE_PUBLIC_KEY,
        secret_key=settings.LANGFUSE_SECRET_KEY,
        host=settings.LANGFUSE_HOST,
    )


# SSE响应头配置
SSE_HEADERS = {
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "text/event-stream",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, GET",
    "Access-Control-Allow-Headers": "Content-Type, Cache-Control",
}
