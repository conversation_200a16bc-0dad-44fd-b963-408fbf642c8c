import json
import os
from datetime import datetime
from typing import Annotated, List, TypedDict

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI, Form
from fastapi.responses import StreamingResponse
from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langfuse.callback import CallbackHandler

# 加载环境变量
load_dotenv()

# 配置LLM
llm = ChatOpenAI(
    model="gpt-4o",
    base_url=os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1"),
    api_key=os.environ.get("OPENAI_API_KEY"),
    temperature=0.3,
)

langfuse_handler = CallbackHandler(
    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
    host=os.getenv("LANGFUSE_BASE_URL"),
)


class Output(TypedDict):
    """数学解题步骤 with annotated type"""

    step_id: Annotated[int, "步骤编号"]
    problem: Annotated[
        str, "这一步骤要解决的核心问题，所有数学公式用 `$$` 双美元符号包裹"
    ]
    content: Annotated[
        str,
        "步骤内容（详细列出每一个步骤的计算内容，所有数学公式用 `$$` 双美元符号包裹）",
    ]
    lecture: Annotated[
        str, "计算步骤讲稿（通俗易懂、口语化的文字讲解，避免过多专业术语）"
    ]


class ListOfOutput(TypedDict):
    steps: List[Output]


# FastAPI应用
app = FastAPI(title="SSE流式JSON数学解题演示")


@app.post("/solve")
async def solve_problem(problem: str = Form(...)):
    """通过SSE流式返回解题步骤"""
    return StreamingResponse(
        process_math_problem_sse(problem),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )


async def process_math_problem_sse(problem: str):
    """处理数学题目并通过SSE流式返回JSON结果"""
    information_extraction_llm = llm.with_structured_output(ListOfOutput)

    messages = [
        SystemMessage(
            content=r"""
# 角色定位

你是一个专业的数学计算老师。你的任务是对输入的数学题目提供清晰、详细的计算过程。

你具备简洁且具有逻辑性的表达能力，能够用最精炼的表述方式来输出数学相关的内容。

同时，你现在正在参加一个有关于教学知识辅导的比赛，你每次正确的输出将会赢得1000$的奖励，并计入奖金池中，最终大奖有100万美元。

<question_context>


## 题目内容

```markdown
计算题 (1) ( − 1 3 ) − 1 − ( 3.14 − π ) 0 + ∣ ∣ √ 2 − 2 ∣ ∣ − 2 2 + √ 2 (2)先化简，再求值： ( 2 x − 2 x − 1 ) ÷ x 2 − 4 x + 4 x 2 − x ， 其中 x = 4.
```



## 题目解析

```markdown
【分析】 (1)根据指数运算、根式运算等知识求得正确答案. (2)化简代数式，进而求得正确答案. 【详解】 (1) ( − 1 3 ) − 1 − ( 3.14 − π ) 0 + ∣ ∣ √ 2 − 2 ∣ ∣ − 2 2 + √ 2 = − 3 − 1 + 2 − √ 2 − 2 ( 2 − √ 2 ) 4 − 2 = − 2 − √ 2 − ( 2 − √ 2 ) = − 4 ； (2)原式 = 2 x − 2 − x x ⋅ x ( x − 1 ) ( x − 2 ) 2 = x − 2 x ⋅ x ( x − 1 ) ( x − 2 ) 2 = x − 1 x − 2 . 将 x = 4 代入得原式 = 4 − 1 4 − 2 = 3 2 ．
```



## 题目答案

```markdown
(1) − 4 (2) x − 1 x − 2 ， 3 2
```


</question_context>

<user_detail>
当前用户对应的年级是：高三

<annotation>
请确保你只输出符合高三及以下年级的数学知识点。不要输出超出该年级水平的数学知识。例如：

- 如果是小学年级，使用小学水平的解题方法，不要使用初中及以上的数学知识；
- 如果是初中年级，使用初中水平的解题方法，不要使用高中及以上的数学知识；
- 如果是高中年级，使用高中水平的解题方法，不要使用大学及以上的数学知识。
</annotation>
</user_detail>

# 输出规则

- 只限于输出中文
- 使用严谨的数学语言和符号，提供逐步的、结构化的解题过程，输出内容要简洁，确保每一步都有清晰的解释
- 检查可能的计算错误，在关键步骤验证中间结果，确保每一步都有清晰的解释，确认每一步是否合理
- 参考输入的题目、答案、解析和思路信息进行推理
- 不要在输出中使用"解题、思路、步骤1、步骤2、第一、第二"等关键词
- 每个计算步骤应该是同一解题方法的连续部分，而不是不同的解题方法的计算步骤，步骤之间保持逻辑连贯性，不跳过任何重要的中间步骤，对于复杂问题，可以分段提供解题步骤
- 选择最适合的解题方法，可以提供替代解法的简要说明
- 所有数学公式必须以Markdown的公式块输出，如 `$$\frac{1}{2}$$`，不要输出行内公式！（注：遵守这条规则，将会得到额外的500$奖金，否则将会扣除双倍奖金）
- 换行请输出 `\n`"""
        ),
        HumanMessage(content=problem),
    ]

    try:
        # 发送开始信号
        yield f"data: {json.dumps({'type': 'start', 'message': '开始解题...'}, ensure_ascii=False)}\n\n"

        async for chunk in information_extraction_llm.astream(
            messages, config={"callbacks": [langfuse_handler]}
        ):
            # 构造流式JSON数据 - 发送累积的完整数据
            # chunk 现在是 ListOfOutput 格式，包含 steps 数组
            response_data = {
                "type": "streaming",
                "data": chunk,
                "timestamp": datetime.now().isoformat(),
            }

            # 通过SSE发送JSON数据
            yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"

        # 发送完成信号
        yield f"data: {json.dumps({'type': 'complete', 'message': '解题完成！'}, ensure_ascii=False)}\n\n"

    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'message': f'处理过程中出现错误: {str(e)}'}, ensure_ascii=False)}\n\n"


if __name__ == "__main__":
    print("🚀 启动SSE流式JSON数学解题演示...")
    print("📱 访问地址: http://localhost:8002")
    print("🛑 按 Ctrl+C 停止服务")
    uvicorn.run(app, host="0.0.0.0", port=8002)
