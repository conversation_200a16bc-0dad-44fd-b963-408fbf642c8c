import asyncio
import os
import re
from typing import Any, List, TypedDict
import uuid

from dotenv import load_dotenv
from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.config import get_stream_writer
from langgraph.constants import END, START
from langgraph.graph import StateGraph
from pydantic import BaseModel, Field

# 加载环境变量
load_dotenv()

# 配置LLM
llm = ChatOpenAI(
    model="gpt-4o",
    base_url=os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1"),
    api_key=os.environ.get("OPENAI_API_KEY"),
    temperature=0.3,
)

from langfuse.callback import CallbackHandler

langfuse_handler = CallbackHandler(
    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
    host=os.getenv("LANGFUSE_HOST"),
)


# Pydantic模型定义
class TopicAnalysis(BaseModel):
    """主题分析结果"""

    main_topics: List[str] = Field(description="主要主题列表")
    theme_clarity_score: int = Field(description="主题明确性得分(0-25)", ge=0, le=25)
    depth_score: int = Field(description="立意深度得分(0-25)", ge=0, le=25)
    relevance_score: int = Field(description="主题相关性得分(0-25)", ge=0, le=25)
    values_score: int = Field(description="价值观导向得分(0-25)", ge=0, le=25)
    feedback: str = Field(description="具体评价和建议")


class StructureAnalysis(BaseModel):
    """结构分析结果"""

    opening_score: int = Field(description="开头效果得分(0-25)", ge=0, le=25)
    paragraph_score: int = Field(description="段落层次得分(0-25)", ge=0, le=25)
    transition_score: int = Field(description="过渡衔接得分(0-25)", ge=0, le=25)
    ending_score: int = Field(description="结尾效果得分(0-25)", ge=0, le=25)
    feedback: str = Field(description="具体评价和建议")


class ContentAnalysis(BaseModel):
    """内容分析结果"""

    material_score: int = Field(description="材料丰富性得分(0-25)", ge=0, le=25)
    logic_score: int = Field(description="论证逻辑得分(0-25)", ge=0, le=25)
    innovation_score: int = Field(description="创新性得分(0-25)", ge=0, le=25)
    depth_score: int = Field(description="深刻性得分(0-25)", ge=0, le=25)
    feedback: str = Field(description="具体评价和建议")


class LanguageAnalysis(BaseModel):
    """语言分析结果"""

    accuracy_score: int = Field(description="语言准确性得分(0-25)", ge=0, le=25)
    fluency_score: int = Field(description="语言流畅性得分(0-25)", ge=0, le=25)
    vividness_score: int = Field(description="语言生动性得分(0-25)", ge=0, le=25)
    standard_score: int = Field(description="语言规范性得分(0-25)", ge=0, le=25)
    feedback: str = Field(description="具体评价和建议")
    errors: List[str] = Field(description="发现的具体错误列表", default_factory=list)


# 创建结构化输出的LLM实例
topic_llm = llm.with_structured_output(TopicAnalysis)
structure_llm = llm.with_structured_output(StructureAnalysis)
content_llm = llm.with_structured_output(ContentAnalysis)
language_llm = llm.with_structured_output(LanguageAnalysis)


class State(TypedDict):
    article: str
    word_count: int
    topic_analysis: dict[str, Any]
    structure_analysis: dict[str, Any]
    content_analysis: dict[str, Any]
    language_analysis: dict[str, Any]
    deduction_details: dict[str, Any]
    final_score: dict[str, Any]
    summary: str
    progress_info: dict[str, Any]


async def word_count_node(state: State):
    """统计字数并进行基础分析"""
    article = state["article"]
    writer = get_stream_writer()

    # 发送进度信息
    writer({"type": "progress", "step": "word_count", "message": "正在统计文章字数..."})

    # 统计字数（去除标点符号和空格）
    clean_text = re.sub(r"[^\u4e00-\u9fa5a-zA-Z0-9]", "", article)
    word_count = len(clean_text)

    # 字数评估
    word_score = 0
    word_feedback = ""

    if word_count >= 800:
        word_score = 10
        word_feedback = "字数充足，符合要求"
    elif word_count >= 600:
        word_score = 8
        word_feedback = "字数基本符合要求"
    elif word_count >= 400:
        word_score = 6
        word_feedback = "字数略少，建议增加内容"
    else:
        word_score = 4
        word_feedback = "字数严重不足"

    writer(
        {
            "type": "result",
            "step": "word_count",
            "word_count": word_count,
            "score": word_score,
        }
    )

    return {
        "word_count": word_count,
        "progress_info": {
            "word_count": {
                "count": word_count,
                "score": word_score,
                "feedback": word_feedback,
            }
        },
    }


async def topic_analysis_node(state: State):
    """分析文章主题和立意"""
    article = state["article"]
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "topic_analysis",
            "message": "正在分析文章主题和立意...",
        }
    )

    system_prompt = """你是一位专业的作文评分专家，专门负责分析文章的主题和立意。

请从以下四个维度对文章进行评价：
1. 主题明确性（是否有明确的中心思想）- 评分范围0-25分
2. 立意深度（思想深度和见解独特性）- 评分范围0-25分
3. 主题相关性（内容是否围绕主题展开）- 评分范围0-25分
4. 价值观导向（是否传递正确的价值观）- 评分范围0-25分

请客观公正地评分，并提供具体的评价和改进建议。"""

    human_prompt = f"请分析以下文章的主题和立意：\n\n{article}"

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=human_prompt),
    ]

    analysis_result = await topic_llm.ainvoke(messages)

    total_score = (
        analysis_result.theme_clarity_score
        + analysis_result.depth_score
        + analysis_result.relevance_score
        + analysis_result.values_score
    )

    writer(
        {
            "type": "result",
            "step": "topic_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"topic_analysis": analysis_result.model_dump()}


async def structure_analysis_node(state: State):
    """分析文章结构"""
    article = state["article"]
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "structure_analysis",
            "message": "正在分析文章结构...",
        }
    )

    system_prompt = """你是一位专业的作文评分专家，专门负责分析文章的结构。

请从以下四个维度对文章结构进行评价：
1. 开头效果（是否引人入胜，点题明确）- 评分范围0-25分
2. 段落层次（段落划分是否合理，层次是否清晰）- 评分范围0-25分
3. 过渡衔接（段落间的过渡是否自然）- 评分范围0-25分
4. 结尾效果（是否呼应开头，总结有力）- 评分范围0-25分

请客观公正地评分，并提供具体的评价和改进建议。"""

    human_prompt = f"请分析以下文章的结构：\n\n{article}"

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=human_prompt),
    ]

    analysis_result = await structure_llm.ainvoke(messages)

    total_score = (
        analysis_result.opening_score
        + analysis_result.paragraph_score
        + analysis_result.transition_score
        + analysis_result.ending_score
    )

    writer(
        {
            "type": "result",
            "step": "structure_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"structure_analysis": analysis_result.model_dump()}


async def content_analysis_node(state: State):
    """分析文章内容质量"""
    article = state["article"]
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "content_analysis",
            "message": "正在分析文章内容质量...",
        }
    )

    system_prompt = """你是一位专业的作文评分专家，专门负责分析文章的内容质量。

请从以下四个维度对文章内容进行评价：
1. 材料丰富性（事例、论据是否充分）- 评分范围0-25分
2. 论证逻辑（推理是否严密，逻辑是否清晰）- 评分范围0-25分
3. 创新性（观点是否新颖，表达是否独特）- 评分范围0-25分
4. 深刻性（分析是否深入，见解是否深刻）- 评分范围0-25分

请客观公正地评分，并提供具体的评价和改进建议。"""

    human_prompt = f"请分析以下文章的内容质量：\n\n{article}"

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=human_prompt),
    ]

    analysis_result = await content_llm.ainvoke(messages)

    total_score = (
        analysis_result.material_score
        + analysis_result.logic_score
        + analysis_result.innovation_score
        + analysis_result.depth_score
    )

    writer(
        {
            "type": "result",
            "step": "content_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"content_analysis": analysis_result.model_dump()}


async def language_analysis_node(state: State):
    """分析文章语言表达"""
    article = state["article"]
    writer = get_stream_writer()

    writer(
        {
            "type": "progress",
            "step": "language_analysis",
            "message": "正在分析文章语言表达...",
        }
    )

    system_prompt = """你是一位专业的作文评分专家，专门负责分析文章的语言表达。

请从以下四个维度对文章语言进行评价：
1. 语言准确性（用词是否准确，语法是否正确）- 评分范围0-25分
2. 语言流畅性（句子是否通顺，表达是否流畅）- 评分范围0-25分
3. 语言生动性（修辞手法运用，语言是否生动）- 评分范围0-25分
4. 语言规范性（标点符号使用，格式是否规范）- 评分范围0-25分

请客观公正地评分，并提供具体的评价和改进建议。如果发现语言错误，请在errors字段中列出。"""

    human_prompt = f"请分析以下文章的语言表达：\n\n{article}"

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=human_prompt),
    ]

    analysis_result = await language_llm.ainvoke(messages)

    total_score = (
        analysis_result.accuracy_score
        + analysis_result.fluency_score
        + analysis_result.vividness_score
        + analysis_result.standard_score
    )

    writer(
        {
            "type": "result",
            "step": "language_analysis",
            "score": total_score,
            "details": analysis_result.model_dump(),
        }
    )

    return {"language_analysis": analysis_result.model_dump()}


async def final_scoring_node(state: State):
    """计算最终得分"""
    writer = get_stream_writer()

    writer(
        {"type": "progress", "step": "final_scoring", "message": "正在计算最终得分..."}
    )

    # 获取各项分析结果
    word_count_info = state.get("progress_info", {}).get("word_count", {})
    topic_analysis = state.get("topic_analysis", {})
    structure_analysis = state.get("structure_analysis", {})
    content_analysis = state.get("content_analysis", {})
    language_analysis = state.get("language_analysis", {})

    # 计算各维度得分
    word_score = word_count_info.get("score", 0)

    topic_score = (
        topic_analysis.get("theme_clarity_score", 0)
        + topic_analysis.get("depth_score", 0)
        + topic_analysis.get("relevance_score", 0)
        + topic_analysis.get("values_score", 0)
    )

    structure_score = (
        structure_analysis.get("opening_score", 0)
        + structure_analysis.get("paragraph_score", 0)
        + structure_analysis.get("transition_score", 0)
        + structure_analysis.get("ending_score", 0)
    )

    content_score = (
        content_analysis.get("material_score", 0)
        + content_analysis.get("logic_score", 0)
        + content_analysis.get("innovation_score", 0)
        + content_analysis.get("depth_score", 0)
    )

    language_score = (
        language_analysis.get("accuracy_score", 0)
        + language_analysis.get("fluency_score", 0)
        + language_analysis.get("vividness_score", 0)
        + language_analysis.get("standard_score", 0)
    )

    # 计算总分（满分500分）
    total_score = (
        word_score + topic_score + structure_score + content_score + language_score
    )

    # 计算等级
    if total_score >= 450:
        grade = "优秀"
    elif total_score >= 400:
        grade = "良好"
    elif total_score >= 350:
        grade = "中等"
    elif total_score >= 300:
        grade = "及格"
    else:
        grade = "不及格"

    final_score_data = {
        "word_score": word_score,
        "topic_score": topic_score,
        "structure_score": structure_score,
        "content_score": content_score,
        "language_score": language_score,
        "total_score": total_score,
        "grade": grade,
        "max_score": 500,
    }

    writer(
        {
            "type": "result",
            "step": "final_scoring",
            "score": total_score,
            "grade": grade,
            "details": final_score_data,
        }
    )

    return {"final_score": final_score_data}


async def summary_node(state: State):
    """生成评价总结"""
    writer = get_stream_writer()

    writer({"type": "progress", "step": "summary", "message": "正在生成评价总结..."})

    # 获取所有分析结果
    word_count = state.get("word_count", 0)
    topic_analysis = state.get("topic_analysis", {})
    structure_analysis = state.get("structure_analysis", {})
    content_analysis = state.get("content_analysis", {})
    language_analysis = state.get("language_analysis", {})
    final_score = state.get("final_score", {})

    prompt = f"""
    请基于以下详细的文章分析结果，生成一份综合性的评价总结报告：

    基本信息：
    - 文章字数：{word_count}
    - 最终得分：{final_score.get("total_score", 0)}/{final_score.get("max_score", 500)}
    - 评价等级：{final_score.get("grade", "未知")}

    各维度得分：
    - 字数得分：{final_score.get("word_score", 0)}/10
    - 主题立意：{final_score.get("topic_score", 0)}/100
    - 文章结构：{final_score.get("structure_score", 0)}/100
    - 内容质量：{final_score.get("content_score", 0)}/100
    - 语言表达：{final_score.get("language_score", 0)}/100

    详细分析：
    主题分析：{topic_analysis.get("feedback", "")}
    结构分析：{structure_analysis.get("feedback", "")}
    内容分析：{content_analysis.get("feedback", "")}
    语言分析：{language_analysis.get("feedback", "")}

    请生成一份包含以下内容的评价总结：
    1. 文章整体评价
    2. 主要优点
    3. 存在问题
    4. 改进建议
    5. 总体评语

    要求：语言简洁明了，评价客观公正，建议具体可行。
    """

    response = await llm.ainvoke([HumanMessage(content=prompt)])

    writer({"type": "result", "step": "summary", "content": response.content})

    return {"summary": response.content}


# 构建评分流程图
graph = (
    StateGraph(State)
    .add_node(word_count_node)
    .add_node(topic_analysis_node)
    .add_node(structure_analysis_node)
    .add_node(content_analysis_node)
    .add_node(language_analysis_node)
    .add_node(final_scoring_node)
    .add_node(summary_node)
    .add_edge(START, "word_count_node")
    .add_edge("word_count_node", "topic_analysis_node")
    .add_edge("topic_analysis_node", "structure_analysis_node")
    .add_edge("structure_analysis_node", "content_analysis_node")
    .add_edge("content_analysis_node", "language_analysis_node")
    .add_edge("language_analysis_node", "final_scoring_node")
    .add_edge("final_scoring_node", "summary_node")
    .add_edge("summary_node", END)
    .compile(name="文章评分Graph测试")
)


async def main():
    """主函数，演示作文评分流程"""
    # 示例文章
    sample_article = """
    那一刻，我豁然开朗
这件事还要从刚刚进入初中的那一个月说起，新组建的班级需要招募几个“临时工”班委来管理这一个月的各项事务，在这一个月过去后会从工作积极认真的“临时工”中筛选一批同学，竞选今后一年的班委。那一刻，我为班级服务的一腔热血仿佛被点燃了，立刻积极地报了名，迫切地想为班级共献自己的一份力量。同时，能当个“一官半职”，岂不更好？
幸福来得太突然，我以这个班级的代理班长身份进行班级展示视频的拍摄与制作，其实我并不知道班长的本职工作是什么，只是把心成浸在视频的拍摄中。从此校园里多了这样的一些场景：晨曦校园一角，我们几个拍摄小草身上晶莹的露珠；午后教室窗台上陪伴我们一起听课的蝴蝶成了我们视频里的常客；傍晚时分球场旁香樟树下的落日余晖都成为了我摄像机中的客人——
一个月的时间转瞬即逝，我代理班长的身份也即将迎来蜕变，学校要求我们每个人准备一个5分钟的演讲，之后让班级同学投票，票数多者胜出，
我陷入了焦虑与迷惘，事实上我根本不会演讲，也不知道该说什么，我经常在准备演讲时走神，晨曦校园一角小草身上没有了晶莹的露珠，午后教室窗台上陪伴我们一起听课的蝴蝶飞走了，傍晚时分球场旁香樟树下的落日余晖也迎来了不眠的黑夜。面对着一个无助而恐惧的自己，在睡梦中也能感受到自己即将被波夺代理班长职位的绝望与迷惘，
班主任似乎注意到了我的变化，在谈话中，我说出了我的心声，班主任则耐心地启发我，在当代理班长时，感觉最大的收获是什么？我沉思良久，回答说:“是为同学们做事的满足与乐趣”。班主任说？“这就是价值，你应该勇敢面对的是如何持续输出自己的价值，班长不班长并不重要，唯有卸下承重的取壳才能让精神自由飞翔！”
那一刻，老师的话我豁然开朗，只要持续输出你的价值，这个世界回报你的不止于名分！诚斯然也，惟有卸下包袱，才能轻装上阵，唯有淡化名利！才能在呈现价值的成长历程中勇敢面对自己，以期未来遇见更好的自己！ 
    """

    initial_state = {"article": sample_article.strip()}

    print("🎯 开始作文评分流程...")
    print("=" * 50)

    async for mode, data in graph.astream(
        initial_state, stream_mode=["updates", "custom"],
        config={"configurable": {"thread_id": uuid.uuid4().hex}, "callbacks": [langfuse_handler]}
    ):
        # 处理流式输出
        if mode == "custom":
            if data.get("type") == "progress":
                step = data.get("step", "")
                message = data.get("message", "")
                print(f"📝 [{step.upper()}] {message}")
            elif data.get("type") == "result":
                step = data.get("step", "")
                if step == "word_count":
                    count = data.get("word_count", 0)
                    score = data.get("score", 0)
                    print(f"✅ 字数统计完成: {count}字 (得分: {score}/10)")
                elif step in [
                    "topic_analysis",
                    "structure_analysis",
                    "content_analysis",
                    "language_analysis",
                ]:
                    score = data.get("score", 0)
                    step_names = {
                        "topic_analysis": "主题分析",
                        "structure_analysis": "结构分析",
                        "content_analysis": "内容分析",
                        "language_analysis": "语言分析",
                    }
                    step_name = step_names.get(step, step)
                    print(f"✅ {step_name}完成 (得分: {score}/100)")
                elif step == "final_scoring":
                    score = data.get("score", 0)
                    grade = data.get("grade", "")
                    print(f"🎉 最终评分完成: {score}/500 ({grade})")
                elif step == "summary":
                    print("📋 评价总结已生成")

        elif mode == "updates":
            for node_name, node_output in data.items():
                print(f"\n🔄 节点 [{node_name}] 执行完成，输出内容:")
                print("-" * 40)

                # 根据不同节点类型显示相关信息
                if node_name == "word_count_node":
                    word_count = node_output.get("word_count", 0)
                    progress_info = node_output.get("progress_info", {}).get("word_count", {})
                    print(f"📊 字数统计结果: {word_count}字")
                    print(f"📈 字数得分: {progress_info.get('score', 0)}/10")
                    print(f"💬 评价: {progress_info.get('feedback', '')}")

                elif node_name in ["topic_analysis_node", "structure_analysis_node", "content_analysis_node", "language_analysis_node"]:
                    analysis_key = node_name.replace("_node", "")
                    analysis_data = node_output.get(analysis_key, {})

                    node_names = {
                        "topic_analysis_node": "主题分析",
                        "structure_analysis_node": "结构分析",
                        "content_analysis_node": "内容分析",
                        "language_analysis_node": "语言分析"
                    }

                    print(f"📊 {node_names[node_name]}结果:")

                    # 显示各项得分
                    if analysis_key == "topic_analysis":
                        print(f"  - 主题明确性: {analysis_data.get('theme_clarity_score', 0)}/25")
                        print(f"  - 立意深度: {analysis_data.get('depth_score', 0)}/25")
                        print(f"  - 主题相关性: {analysis_data.get('relevance_score', 0)}/25")
                        print(f"  - 价值观导向: {analysis_data.get('values_score', 0)}/25")
                        if analysis_data.get('main_topics'):
                            print(f"  - 主要主题: {', '.join(analysis_data['main_topics'])}")
                    elif analysis_key == "structure_analysis":
                        print(f"  - 开头效果: {analysis_data.get('opening_score', 0)}/25")
                        print(f"  - 段落层次: {analysis_data.get('paragraph_score', 0)}/25")
                        print(f"  - 过渡衔接: {analysis_data.get('transition_score', 0)}/25")
                        print(f"  - 结尾效果: {analysis_data.get('ending_score', 0)}/25")
                    elif analysis_key == "content_analysis":
                        print(f"  - 材料丰富性: {analysis_data.get('material_score', 0)}/25")
                        print(f"  - 论证逻辑: {analysis_data.get('logic_score', 0)}/25")
                        print(f"  - 创新性: {analysis_data.get('innovation_score', 0)}/25")
                        print(f"  - 深刻性: {analysis_data.get('depth_score', 0)}/25")
                    elif analysis_key == "language_analysis":
                        print(f"  - 语言准确性: {analysis_data.get('accuracy_score', 0)}/25")
                        print(f"  - 语言流畅性: {analysis_data.get('fluency_score', 0)}/25")
                        print(f"  - 语言生动性: {analysis_data.get('vividness_score', 0)}/25")
                        print(f"  - 语言规范性: {analysis_data.get('standard_score', 0)}/25")
                        if analysis_data.get('errors'):
                            print(f"  - 发现错误: {len(analysis_data['errors'])}个")

                    # 显示反馈
                    feedback = analysis_data.get('feedback', '')
                    if feedback:
                        print(f"� 专家评价: {feedback[:100]}{'...' if len(feedback) > 100 else ''}")

                elif node_name == "final_scoring_node":
                    final_score = node_output.get("final_score", {})
                    print(f"🎯 最终评分详情:")
                    print(f"  - 字数得分: {final_score.get('word_score', 0)}/10")
                    print(f"  - 主题立意: {final_score.get('topic_score', 0)}/100")
                    print(f"  - 文章结构: {final_score.get('structure_score', 0)}/100")
                    print(f"  - 内容质量: {final_score.get('content_score', 0)}/100")
                    print(f"  - 语言表达: {final_score.get('language_score', 0)}/100")
                    print(f"🏆 总分: {final_score.get('total_score', 0)}/{final_score.get('max_score', 500)} ({final_score.get('grade', '未知')})")

                elif node_name == "summary_node":
                    summary = node_output.get("summary", "")
                    print("\n" + "=" * 50)
                    print("📊 最终评价报告:")
                    print("=" * 50)
                    print(summary)

                print("-" * 40)


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
