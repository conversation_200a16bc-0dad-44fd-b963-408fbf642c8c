#!/usr/bin/env python3
"""
基于 demo2.py graph workflow 的 SSE 服务器
"""

import asyncio
import json
import uuid
from typing import AsyncGenerator

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

# 导入 demo2.py 中的 graph
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from structure_article_review_demo import graph
    print("✅ 成功导入 demo2.py 中的 graph")
except ImportError as e:
    print(f"❌ 导入 demo2.py 失败: {e}")
    # 创建一个模拟的graph用于测试
    graph = None

from langfuse.callback import CallbackHandler

langfuse_handler = CallbackHandler(
    public_key="pk-lf-fae0c7b5-9678-4955-beb7-8ede32ca9548",
    secret_key="sk-lf-840d6b75-fc6a-4808-828c-13a2ebec5e1f",
    host="https://langfuse.xiaoxingcloud.com",
)

# 请求模型
class EssayRequest(BaseModel):
    article: str

app = FastAPI(title="作文评分SSE API - 基于LangGraph")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def run_essay_scoring_graph(article: str) -> AsyncGenerator[str, None]:
    """使用真实的 LangGraph workflow 进行作文评分，生成SSE事件流"""

    if not article.strip():
        yield f"data: {json.dumps({'type': 'error', 'message': '文章内容不能为空'})}\n\n"
        return

    initial_state = {"article": article.strip()}

    # 用于收集最终结果
    final_result = {
        "wordCount": 0,
        "topicAnalysis": None,
        "structureAnalysis": None,
        "contentAnalysis": None,
        "languageAnalysis": None,
        "finalScore": None,
        "summary": None
    }

    try:
        async for mode, data in graph.astream(
            initial_state,
            stream_mode=["updates", "custom"],
            config={"configurable": {"thread_id": uuid.uuid4().hex}, "callbacks": [langfuse_handler]}
        ):
            # 处理自定义流式输出（进度和结果）
            if mode == "custom":
                if data.get("type") == "progress":
                    # 转发进度信息
                    yield f"data: {json.dumps(data)}\n\n"

                elif data.get("type") == "result":
                    # 转发结果信息
                    step = data.get("step", "")

                    # 收集结果数据用于最终汇总
                    if step == "word_count":
                        final_result["wordCount"] = data.get("word_count", 0)
                    elif step == "topic_analysis":
                        final_result["topicAnalysis"] = data.get("details", {})
                    elif step == "structure_analysis":
                        final_result["structureAnalysis"] = data.get("details", {})
                    elif step == "content_analysis":
                        final_result["contentAnalysis"] = data.get("details", {})
                    elif step == "language_analysis":
                        final_result["languageAnalysis"] = data.get("details", {})
                    elif step == "final_scoring":
                        final_result["finalScore"] = data.get("details", {})
                    elif step == "summary":
                        final_result["summary"] = data.get("content", "")

                    # 转发结果，并包含当前的累积结果
                    enhanced_data = {
                        **data,
                        "currentResults": final_result.copy()  # 包含到目前为止的所有结果
                    }
                    yield f"data: {json.dumps(enhanced_data)}\n\n"

            # 处理节点更新，收集完整的状态数据
            elif mode == "updates":
                for node_name, node_output in data.items():
                    # 从节点输出中收集数据，确保数据完整性
                    if node_name == "word_count_node":
                        final_result["wordCount"] = node_output.get("word_count", 0)
                    elif node_name == "topic_analysis_node":
                        final_result["topicAnalysis"] = node_output.get("topic_analysis", {})
                    elif node_name == "structure_analysis_node":
                        final_result["structureAnalysis"] = node_output.get("structure_analysis", {})
                    elif node_name == "content_analysis_node":
                        final_result["contentAnalysis"] = node_output.get("content_analysis", {})
                    elif node_name == "language_analysis_node":
                        final_result["languageAnalysis"] = node_output.get("language_analysis", {})
                    elif node_name == "final_scoring_node":
                        final_result["finalScore"] = node_output.get("final_score", {})
                    elif node_name == "summary_node":
                        final_result["summary"] = node_output.get("summary", "")

        # 发送完成信号和最终结果
        yield f"data: {json.dumps({'type': 'complete', 'result': final_result})}\n\n"

    except Exception as e:
        error_msg = f"评分过程中出现错误: {str(e)}"
        yield f"data: {json.dumps({'type': 'error', 'message': error_msg})}\n\n"
    
@app.post("/score-essay")
async def score_essay(request: EssayRequest):
    """作文评分SSE端点 - 使用真实的LangGraph workflow"""
    if not request.article.strip():
        raise HTTPException(status_code=400, detail="文章内容不能为空")

    return StreamingResponse(
        run_essay_scoring_graph(request.article),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST",
            "Access-Control-Allow-Headers": "Content-Type",
        }
    )


@app.get("/")
async def root():
    return {
        "message": "作文评分SSE API服务器运行中",
        "description": "基于LangGraph的智能作文评分系统",
        "endpoints": {
            "/score-essay": "作文评分SSE端点，参数: article (文章内容)"
        }
    }


if __name__ == "__main__":
    import uvicorn
    print("🚀 启动作文评分SSE服务器...")
    print("📡 服务地址: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
