#!/usr/bin/env python3
"""
数学解题相关的数据模型
"""

from typing import Annotated, List, TypedDict

from pydantic import BaseModel


class MathStep(TypedDict):
    """数学解题步骤"""

    step_id: Annotated[int, "步骤编号"]
    problem: Annotated[
        str, "这一步骤要解决的核心问题，所有数学公式用 `$$` 双美元符号包裹"
    ]
    content: Annotated[
        str,
        "步骤内容（详细列出每一个步骤的计算内容，所有数学公式用 `$$` 双美元符号包裹）",
    ]
    lecture: Annotated[
        str, "计算步骤讲稿（通俗易懂、口语化的文字讲解，避免过多专业术语）"
    ]


class MathSolution(TypedDict):
    """数学解题方案"""

    steps: List[MathStep]


class MathRequest(BaseModel):
    """数学解题请求模型"""

    problem: str
