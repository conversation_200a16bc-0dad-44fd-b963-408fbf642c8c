#!/usr/bin/env python3
"""
数学解题服务
"""

from typing import AsyncGenerator

from config import get_langfuse_handler, get_llm
from sse_utils import (
    create_complete_event,
    create_error_event,
    create_start_event,
    create_streaming_event,
)

from mathsolver.models import MathSolution
from mathsolver.prompts import get_math_solving_messages


async def process_math_solving(problem: str) -> AsyncGenerator[str, None]:
    """处理数学解题并通过SSE流式返回结果"""

    if not problem.strip():
        yield create_error_event("题目内容不能为空")
        return

    try:
        # 发送开始信号
        yield create_start_event("开始解题...")

        llm = get_llm().with_structured_output(MathSolution)
        langfuse_handler = get_langfuse_handler()

        messages = get_math_solving_messages(problem)

        # 流式处理
        async for chunk in llm.astream(
            messages, config={"callbacks": [langfuse_handler]}
        ):
            # 发送流式数据
            yield create_streaming_event(chunk)

        # 发送完成信号
        yield create_complete_event("解题完成！")

    except Exception as e:
        yield create_error_event(f"解题过程中出现错误: {str(e)}")
