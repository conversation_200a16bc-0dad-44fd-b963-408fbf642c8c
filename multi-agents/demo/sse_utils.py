#!/usr/bin/env python3
"""
SSE工具函数
"""

import json
from datetime import datetime
from typing import Any

try:
    from pydantic import BaseModel

    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseModel = None


class SSEJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，支持Pydantic模型和其他特殊对象"""

    def default(self, obj):
        # 处理Pydantic BaseModel对象
        if PYDANTIC_AVAILABLE and isinstance(obj, BaseModel):
            return obj.model_dump()

        # 处理datetime对象
        if isinstance(obj, datetime):
            return obj.isoformat()

        # 处理其他有dict()方法的对象
        if hasattr(obj, "dict") and callable(getattr(obj, "dict")):
            return obj.dict()

        # 处理其他有model_dump()方法的对象
        if hasattr(obj, "model_dump") and callable(getattr(obj, "model_dump")):
            return obj.model_dump()

        # 回退到默认处理
        return super().default(obj)


def create_sse_event(event_type: str, data: Any = None, message: str = None) -> str:
    """创建SSE事件字符串"""
    event_data = {"type": event_type, "timestamp": datetime.now().isoformat()}

    if data is not None:
        event_data["data"] = data

    if message is not None:
        event_data["message"] = message

    return f"data: {json.dumps(event_data, ensure_ascii=False, cls=SSEJSONEncoder)}\n\n"


def create_start_event(message: str = "开始处理...") -> str:
    """创建开始事件"""
    return create_sse_event("start", message=message)


def create_progress_event(step: str, message: str, data: Any = None) -> str:
    """创建进度事件"""
    event_data = {"step": step, "message": message}
    if data is not None:
        event_data.update(data)

    return create_sse_event("progress", data=event_data)


def create_result_event(step: str, data: Any) -> str:
    """创建结果事件"""
    return create_sse_event("result", data={"step": step, **data})


def create_streaming_event(data: Any) -> str:
    """创建流式数据事件"""
    return create_sse_event("streaming", data=data)


def create_complete_event(message: str = "处理完成！") -> str:
    """创建完成事件"""
    return create_sse_event("complete", message=message)


def create_error_event(message: str, error_code: str = None) -> str:
    """创建错误事件"""
    error_data = {"message": message}
    if error_code:
        error_data["error_code"] = error_code

    return create_sse_event("error", data=error_data)
