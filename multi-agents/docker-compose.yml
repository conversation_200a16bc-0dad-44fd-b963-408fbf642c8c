version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./demo
      dockerfile: Dockerfile
    container_name: multi-agents-backend
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_BASE_URL=${LANGFUSE_BASE_URL}
    volumes:
      - ./demo:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - multi-agents-network

  # 前端Web服务
  frontend:
    build:
      context: ./webui
      dockerfile: Dockerfile
    container_name: multi-agents-frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    volumes:
      - ./webui:/app
      - /app/node_modules
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - multi-agents-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: multi-agents-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    networks:
      - multi-agents-network
    profiles:
      - production

networks:
  multi-agents-network:
    driver: bridge

volumes:
  node_modules:
