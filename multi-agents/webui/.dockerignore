# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建产物
dist
dist-ssr
*.local

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE和编辑器
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 测试相关
coverage
*.lcov
.nyc_output

# 运行时文件
*.pid
*.seed
*.pid.lock

# 日志文件
logs
*.log

# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
.dockerignore

# 文档
README.md
docs/
*.md

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ESLint
.eslintcache

# TypeScript
*.tsbuildinfo

# Storybook
.storybook-out
storybook-static
