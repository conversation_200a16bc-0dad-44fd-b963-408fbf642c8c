# 多功能AI演示系统 (重构版)

这是一个基于现代React技术栈构建的多功能AI演示系统，包含文章评分和数学解题功能。

## ✨ 重构亮点

### 🎯 代码组织优化
- **组件复用**: 使用通用的`StreamingDemo`组件减少重复代码
- **Hook简化**: 使用`react-use`库替代自定义hooks
- **类型安全**: 统一的TypeScript类型定义
- **现代化UI**: 基于Tailwind CSS的响应式设计

### 📦 技术栈升级
- **React 18** + TypeScript
- **Vite** 构建工具
- **Tailwind CSS** + CSS变量支持
- **react-use** 实用hooks库
- **react-markdown** + KaTeX 数学公式渲染
- **Server-Sent Events** 流式数据处理

### 🏗️ 架构改进
- **统一的流式API处理**: `useStreamingAPI` hook
- **可复用的UI组件**: Button, Card, Textarea等
- **模块化设计**: 清晰的文件结构和职责分离
- **类型驱动开发**: 完整的TypeScript支持

## 功能特色

### 📝 文章评分
- 实时流式评分过程
- 多维度分析结果
- 智能反馈建议

### 🧮 数学解题
- 步骤化解题过程
- LaTeX公式渲染
- 详细解题讲解

## 技术栈

- **前端**: React 18 + TypeScript + Vite
- **样式**: Tailwind CSS + CSS Variables
- **工具库**: react-use, clsx, class-variance-authority
- **数学渲染**: react-markdown + rehype-katex
- **路由**: React Router DOM v6
- **流式数据**: Server-Sent Events

## 项目结构

```
## 📁 项目结构

```
webui/
├── src/
│   ├── components/           # React组件
│   │   ├── common/          # 通用组件
│   │   │   └── StreamingDemo.tsx  # 流式演示通用组件
│   │   ├── ui/              # 基础UI组件
│   │   │   ├── button.tsx   # 按钮组件
│   │   │   ├── card.tsx     # 卡片组件
│   │   │   ├── textarea.tsx # 文本域组件
│   │   │   └── ...          # 其他UI组件
│   │   ├── layout/          # 布局组件
│   │   ├── EssayScoring.tsx # 文章评分页面
│   │   ├── MathTest.tsx     # 数学解题页面
│   │   ├── Home.tsx         # 首页
│   │   └── Navigation.tsx   # 导航栏
│   ├── hooks/               # 自定义Hooks
│   │   └── useStreamingAPI.ts # 流式API Hook
│   ├── lib/                 # 工具库
│   │   ├── utils.ts         # 通用工具函数
│   │   └── api.ts           # API客户端
│   ├── types/               # TypeScript类型定义
│   │   └── index.ts         # 统一类型导出
│   └── main.tsx             # 应用入口
└── ../demo/                 # 后端服务
    ├── app.py              # 统一API服务器
    ├── article/            # 文章评分模块
    ├── mathsolver/         # 数学解题模块
    └── config.py           # 配置管理
```
```

## 开发指南

### 启动开发服务器

```bash
cd webui
npm install
npm run dev
```

应用将在 http://localhost:3000 启动

### 启动后端服务

AI功能需要统一的后端服务支持：

```bash
cd ../demo
python app.py
```

后端服务将在 http://localhost:8000 启动，提供以下API端点：
- `/api/v1/essay/score` - 文章评分
- `/api/v1/math/solve` - 数学解题
- `/health` - 健康检查
- `/docs` - API文档

### 构建生产版本

```bash
npm run build
```

## 使用说明

### 文章评分功能

1. 访问 http://localhost:3000/review-article
2. 在文本框中输入文章内容
3. 点击"开始评分"按钮
4. 实时查看评分过程和详细结果
5. 查看主题、结构、内容、语言等维度的评分

### 数学解题功能

1. 访问 http://localhost:3000/ai-math
2. 在输入框中输入数学题目
3. 支持LaTeX格式的数学公式，例如：`$$\frac{1}{2}$$`
4. 点击"开始解题"按钮
5. 实时查看解题步骤和详细讲解

## 特性说明

- **响应式设计**: 支持桌面和移动设备
- **实时更新**: 使用SSE技术实现实时数据流
- **数学公式渲染**: 集成MathJax支持LaTeX公式
- **Markdown支持**: 支持富文本内容渲染
- **现代化UI**: 使用Tailwind CSS构建美观界面
- **TypeScript**: 完整的类型安全支持

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 注意事项

- 确保后端服务正常运行
- 数学公式需要使用LaTeX格式
- 建议使用现代浏览器以获得最佳体验
