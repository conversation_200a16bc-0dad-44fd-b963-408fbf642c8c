{"name": "multi-agents-ui", "version": "2.0.0", "description": "Multi-Agent AI Demo UI with Modern React Stack", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "better-react-mathjax": "^2.3.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "katex": "^0.16.9", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.21.3", "react-use": "^17.4.2", "recharts": "^3.1.0", "rehype-katex": "^7.0.0", "remark-math": "^6.0.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.0"}}