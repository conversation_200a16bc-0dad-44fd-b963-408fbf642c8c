import { Route, BrowserRouter as Router, Routes } from 'react-router-dom'
import { Navigation } from './components/Navigation'
import { ArticlePage } from './pages/ArticlePage'
import { HomePage } from './pages/HomePage'
import { MathPage } from './pages/MathPage'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/review-article" element={<ArticlePage />} />
          <Route path="/ai-math" element={<MathPage />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App;
