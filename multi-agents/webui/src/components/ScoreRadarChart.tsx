import React from 'react'
import { PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart, ResponsiveContainer } from 'recharts'

interface ScoreRadarChartProps {
  data: {
    word_score?: number
    topic_score?: number
    structure_score?: number
    content_score?: number
    language_score?: number
  }
}

export const ScoreRadarChart: React.FC<ScoreRadarChartProps> = ({
  data,
}) => {
  const radarData = [
    {
      subject: '字数标点',
      score: data.word_score || 0,
      fullMark: 5
    },
    {
      subject: '主题立意',
      score: data.topic_score || 0,
      fullMark: 15
    },
    {
      subject: '结构层次',
      score: data.structure_score || 0,
      fullMark: 10
    },
    {
      subject: '内容表达',
      score: data.content_score || 0,
      fullMark: 15
    },
    {
      subject: '语言文字',
      score: data.language_score || 0,
      fullMark: 15
    }
  ]

  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart data={radarData}>
          <PolarGrid />
          <PolarAngleAxis 
            dataKey="subject" 
            tick={{ fontSize: 12, fill: '#666' }}
          />
          <PolarRadiusAxis
            angle={90}
            domain={[0, 15]}
            tick={{ fontSize: 10, fill: '#999' }}
          />
          <Radar
            name="得分"
            dataKey="score"
            stroke="#3b82f6"
            fill="#3b82f6"
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default ScoreRadarChart
