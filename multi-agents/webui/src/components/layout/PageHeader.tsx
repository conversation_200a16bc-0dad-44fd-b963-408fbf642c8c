import React from 'react'
import { cn } from '@/lib/utils'

interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  children,
  className
}) => {
  return (
    <div className={cn('mb-8', className)}>
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          {title}
        </h1>
        {description && (
          <p className="text-muted-foreground text-lg mb-4">
            {description}
          </p>
        )}
        {children}
      </div>
    </div>
  )
}
