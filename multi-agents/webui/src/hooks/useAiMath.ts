import { useCallback, useState } from 'react'
import { MathSolution, MathStep, SSEEvent } from '../types'
import { useStreamingApi } from './useStreamingApi'

export function useAiMath() {
  const [currentSteps, setCurrentSteps] = useState<MathStep[]>([])
  const [isComplete, setIsComplete] = useState(false)

  const handleEvent = useCallback((event: SSEEvent) => {
    console.log('Math solver event:', event)
    
    switch (event.type) {
      case 'start':
        setCurrentSteps([])
        setIsComplete(false)
        break
        
      case 'streaming':
        if (event.data?.steps) {
          setCurrentSteps(event.data.steps)
        }
        break
        
      case 'complete':
        setIsComplete(true)
        break
        
      case 'final_result':
        if (event.data?.steps) {
          setCurrentSteps(event.data.steps)
        }
        setIsComplete(true)
        break
    }
  }, [])

  const {
    isLoading,
    events,
    error,
    finalResult,
    startStream,
    stopStream,
    reset: resetStream
  } = useStreamingApi({
    onEvent: handleEvent,
    onComplete: (result) => {
      console.log('Math solving completed:', result)
      setIsComplete(true)
    },
    onError: (error) => {
      console.error('Math solving error:', error)
    }
  })

  const solveProblem = useCallback(async (problem: string) => {
    if (!problem.trim()) {
      throw new Error('题目内容不能为空')
    }

    // 重置状态
    setCurrentSteps([])
    setIsComplete(false)

    await startStream('http://localhost:8000/api/v1/math/solve', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ problem }),
    })
  }, [startStream])

  const reset = useCallback(() => {
    resetStream()
    setCurrentSteps([])
    setIsComplete(false)
  }, [resetStream])

  // 构建完整的解题结果
  const buildSolution = useCallback((): MathSolution | null => {
    if (currentSteps.length === 0 && !finalResult) {
      return null
    }

    const steps = currentSteps.length > 0 ? currentSteps : (finalResult?.steps || [])
    
    return {
      steps: steps
    }
  }, [currentSteps, finalResult])

  return {
    // 状态
    isLoading,
    error,
    isComplete,
    
    // 数据
    events,
    currentSteps,
    solution: buildSolution(),
    
    // 操作
    solveProblem,
    stopStream,
    reset,
  }
}
