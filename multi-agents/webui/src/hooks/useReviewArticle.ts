import { useCallback, useState } from 'react'
import { ArticleScoreResult, SSEEvent } from '../types'
import { useStreaming<PERSON>pi } from './useStreamingApi'

export function useReviewArticle() {
  const [currentStep, setCurrentStep] = useState<string>('')
  const [stepResults, setStepResults] = useState<Record<string, any>>({})
  const [wordCount, setWordCount] = useState<number>(0)
  const [progressInfo, setProgressInfo] = useState<Record<string, any>>({})

  const handleEvent = useCallback((event: SSEEvent) => {
    console.log('Essay scoring event:', event)

    switch (event.type) {
      case 'progress':
        setCurrentStep(event.message || '')
        break

      case 'result':
        if (event.step) {
          // 根据后端实际格式处理数据
          const stepData = event.data || event.details || event

          // 保存每个步骤的结果
          setStepResults(prev => ({
            ...prev,
            [event.step!]: stepData
          }))

          // 处理特定步骤的数据
          if (event.step === 'word_count') {
            const wordCount = stepData.count || stepData.word_count || event.word_count || 0
            setWordCount(wordCount)
            setProgressInfo(prev => ({
              ...prev,
              word_count: stepData
            }))
          } else if (event.step === 'topic_analysis') {
            setProgressInfo(prev => ({
              ...prev,
              topic_analysis: stepData.details || stepData
            }))
          } else if (event.step === 'structure_analysis') {
            setProgressInfo(prev => ({
              ...prev,
              structure_analysis: stepData.details || stepData
            }))
          } else if (event.step === 'content_analysis') {
            setProgressInfo(prev => ({
              ...prev,
              content_analysis: stepData.details || stepData
            }))
          } else if (event.step === 'language_analysis') {
            setProgressInfo(prev => ({
              ...prev,
              language_analysis: stepData.details || stepData
            }))
          } else if (event.step === 'final_scoring') {
            setProgressInfo(prev => ({
              ...prev,
              final_scoring: stepData
            }))
          } else if (event.step === 'summary') {
            setProgressInfo(prev => ({
              ...prev,
              summary: stepData.summary || stepData
            }))
          }
        }
        break

      case 'streaming':
        // 处理流式数据
        if (event.data) {
          setStepResults(prev => ({
            ...prev,
            ...event.data
          }))
        }
        break

      case 'final_result':
        // 最终结果
        if (event.data) {
          setStepResults(prev => ({
            ...prev,
            final: event.data
          }))
        }
        break
    }
  }, [])

  const {
    isLoading,
    events,
    error,
    finalResult,
    startStream,
    stopStream,
    reset: resetStream
  } = useStreamingApi({
    onEvent: handleEvent,
    onComplete: (result) => {
      console.log('Essay scoring completed:', result)
    },
    onError: (error) => {
      console.error('Essay scoring error:', error)
    }
  })

  const scoreEssay = useCallback(async (article: string) => {
    if (!article.trim()) {
      throw new Error('文章内容不能为空')
    }

    // 重置状态
    setCurrentStep('')
    setStepResults({})
    setWordCount(0)
    setProgressInfo({})

    await startStream('http://localhost:8000/api/v1/essay/score', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ article }),
    })
  }, [startStream])

  const reset = useCallback(() => {
    resetStream()
    setCurrentStep('')
    setStepResults({})
    setWordCount(0)
    setProgressInfo({})
  }, [resetStream])

  // 构建完整的评分结果
  const buildScoreResult = useCallback((): ArticleScoreResult | null => {
    // 如果没有任何步骤结果，返回null
    if (Object.keys(stepResults).length === 0 && !finalResult) {
      return null
    }

    // 从各个步骤结果中提取数据
    const finalData = finalResult || stepResults.final || {}
    const finalScoring = stepResults.final_scoring || {}

    // 提取各维度分析结果
    const topicData = stepResults.topic_analysis || {}
    const structureData = stepResults.structure_analysis || {}
    const contentData = stepResults.content_analysis || {}
    const languageData = stepResults.language_analysis || {}
    const summaryData = stepResults.summary || {}

    return {
      word_count: wordCount || finalData.wordCount || 0,
      word_score: finalScoring.word_score || finalData.word_score || 0,
      topic_score: topicData.score || finalScoring.topic_score || finalData.topic_score || 0,
      structure_score: structureData.score || finalScoring.structure_score || finalData.structure_score || 0,
      content_score: contentData.score || finalScoring.content_score || finalData.content_score || 0,
      language_score: languageData.score || finalScoring.language_score || finalData.language_score || 0,
      total_score: finalScoring.total_score || finalData.total_score || 0,
      max_score: finalScoring.max_score || finalData.max_score || 60,
      topic_analysis: topicData.details || topicData || finalData.topicAnalysis || {},
      structure_analysis: structureData.details || structureData || finalData.structureAnalysis || {},
      content_analysis: contentData.details || contentData || finalData.contentAnalysis || {},
      language_analysis: languageData.details || languageData || finalData.languageAnalysis || {},
      summary: summaryData.summary || summaryData || finalData.summary || '',
      deduction_details: finalScoring.deduction_details || finalData.deduction_details || {}
    }
  }, [finalResult, stepResults, wordCount])

  return {
    // 状态
    isLoading,
    error,
    currentStep,
    wordCount,
    
    // 数据
    events,
    stepResults,
    progressInfo,
    scoreResult: buildScoreResult(),
    
    // 操作
    scoreEssay,
    stopStream,
    reset,
  }
}
