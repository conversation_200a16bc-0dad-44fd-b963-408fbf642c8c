import { useCallback, useRef, useState } from 'react'
import { useList, useToggle } from 'react-use'
import { SSEEvent } from '../types'

interface UseStreamingApiOptions {
  onEvent?: (event: SSEEvent) => void
  onComplete?: (finalResult?: any) => void
  onError?: (error: string) => void
}

export function useStreamingApi(options: UseStreamingApiOptions = {}) {
  const [isLoading, toggleLoading] = useToggle(false)
  const [events, { push: addEvent, clear: clearEvents }] = useList<SSEEvent>([])
  const [error, setError] = useState<string | null>(null)
  const [finalResult, setFinalResult] = useState<any>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const startStream = useCallback(async (url: string, requestInit: RequestInit = {}) => {
    // 清理之前的连接
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    toggleLoading(true)
    clearEvents()
    setError(null)
    setFinalResult(null)

    try {
      const response = await fetch(url, {
        ...requestInit,
        signal: abortControllerRef.current.signal,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
          ...requestInit.headers,
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          toggleLoading(false)
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6))
              const event: SSEEvent = {
                ...eventData,
                timestamp: eventData.timestamp || new Date().toISOString()
              }

              addEvent(event)
              options.onEvent?.(event)

              // 处理不同类型的事件
              if (event.type === 'complete') {
                setFinalResult(event.result || event.data)
                toggleLoading(false)
                options.onComplete?.(event.result || event.data)
                break
              } else if (event.type === 'error') {
                const errorMsg = event.message || 'Unknown error occurred'
                setError(errorMsg)
                toggleLoading(false)
                options.onError?.(errorMsg)
                break
              } else if (event.type === 'final_result') {
                setFinalResult(event.data || event.result)
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE event:', line, parseError)
            }
          }
        }
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return // 请求被取消，不设置错误状态
      }

      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toggleLoading(false)
      options.onError?.(errorMessage)
    }
  }, [toggleLoading, clearEvents, addEvent, options])

  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    toggleLoading(false)
  }, [toggleLoading])

  const reset = useCallback(() => {
    stopStream()
    clearEvents()
    setError(null)
    setFinalResult(null)
  }, [stopStream, clearEvents])

  return {
    isLoading,
    events,
    error,
    finalResult,
    startStream,
    stopStream,
    reset,
  }
}
