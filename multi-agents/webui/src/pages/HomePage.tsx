import { Link } from 'react-router-dom';

export const HomePage: React.FC = () => {
  const features = [
    {
      title: '作文评分',
      description: '智能AI作文评分系统，提供详细的评分标准和改进建议',
      icon: '📝',
      path: '/review-article',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'AI数学讲解',
      description: 'SSE流式数学解题演示，实时显示解题步骤和详细讲解',
      icon: '🧮',
      path: '/ai-math',
      color: 'from-green-500 to-green-600',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              多功能AI演示系统
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              体验最新的AI技术，包括作文评分和数学解题功能
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                to="/review-article"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                开始作文评分
              </Link>
              <Link
                to="/ai-math"
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                开始数学解题
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">功能特色</h2>
          <p className="text-gray-600 text-lg">
            探索我们提供的AI驱动的智能功能
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <Link
              key={index}
              to={feature.path}
              className="group block"
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className={`h-32 bg-gradient-to-r ${feature.color} flex items-center justify-center`}>
                  <span className="text-6xl">{feature.icon}</span>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                  <div className="mt-4 flex items-center text-blue-600 font-medium">
                    <span>了解更多</span>
                    <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p className="text-gray-400">
            基于React + TypeScript + Tailwind CSS构建
          </p>
        </div>
      </footer>
    </div>
  );
};
