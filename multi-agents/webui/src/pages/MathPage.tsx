import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { useAiMath } from '@/hooks/useAiMath'
import 'katex/dist/katex.min.css'
import React, { useEffect, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeKatex from 'rehype-katex'
import remarkMath from 'remark-math'



// 数学公式渲染组件 - 调试版本
const MathRenderer: React.FC<{ content?: string; className?: string; inline?: boolean }> = ({
  content = '',
  className = '',
  inline = false
}) => {
  // 如果没有内容，返回空元素
  if (!content) {
    return inline ? <span></span> : <div></div>
  }

  // 调试输出
  console.log('MathRenderer content:', content)

  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath]}
      rehypePlugins={[rehypeKatex]}
      className={className}
    >
      {content}
    </ReactMarkdown>
  )
}

export const MathPage: React.FC = () => {
  const [problem, setProblem] = useState('解方程：2x + 5 = 13')
  const jsonScrollRef = useRef<HTMLDivElement>(null)
  const {
    isLoading,
    error,
    isComplete,
    events,
    currentSteps,
    solution,
    solveProblem,
    stopStream,
    reset
  } = useAiMath()

  // 自动滚动到JSON数据底部
  useEffect(() => {
    if (jsonScrollRef.current) {
      jsonScrollRef.current.scrollTop = jsonScrollRef.current.scrollHeight
    }
  }, [events])

  const handleStart = async () => {
    try {
      await solveProblem(problem)
    } catch (err) {
      console.error('解题失败:', err)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* 标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">AI数学讲解Demo</h1>
          <p className="text-gray-600">基于步骤分解的智能数学讲解Demo</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 输入区域 */}
          <Card>
            <CardHeader>
              <CardTitle>题目输入</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={problem}
                onChange={(e) => setProblem(e.target.value)}
                placeholder="请输入数学题目..."
                className="min-h-[200px] resize-none"
                disabled={isLoading}
              />

              <div className="flex gap-2">
                <Button
                  onClick={handleStart}
                  disabled={isLoading || !problem.trim()}
                  className="flex items-center gap-2"
                >
                  {isLoading && <LoadingSpinner size="sm" />}
                  {isLoading ? '解题中...' : '开始解题'}
                </Button>

                {isLoading && (
                  <Button onClick={stopStream} variant="outline">
                    停止
                  </Button>
                )}

                {(currentSteps.length > 0 || solution) && !isLoading && (
                  <Button onClick={reset} variant="outline">
                    重置
                  </Button>
                )}
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 解题过程区域 */}
          <Card>
            <CardHeader>
              <CardTitle>解题过程</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="json" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="json">JSON数据</TabsTrigger>
                  <TabsTrigger value="logs">过程日志</TabsTrigger>
                </TabsList>

                <TabsContent value="json" className="mt-4">
                  <div ref={jsonScrollRef} className="space-y-2 max-h-96 overflow-y-auto">
                    {events.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        暂无数据
                      </div>
                    ) : (
                      events.map((event, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded border text-xs">
                          <div className="font-mono text-gray-600 mb-1">
                            [{new Date(event.timestamp || Date.now()).toLocaleTimeString()}] {event.type}
                          </div>
                          <pre className="text-gray-800 whitespace-pre-wrap break-words">
                            {JSON.stringify(event, null, 2)}
                          </pre>
                        </div>
                      ))
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="logs" className="mt-4">
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {events.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        暂无日志信息
                      </div>
                    ) : (
                      events.map((event, index) => (
                        <div key={index} className="text-xs p-2 bg-gray-50 rounded border-l-2 border-blue-200">
                          <div className="font-mono text-gray-600">
                            [{new Date(event.timestamp || Date.now()).toLocaleTimeString()}]
                          </div>
                          <div className="text-gray-800">
                            {event.type === 'start' && `🚀 ${event.message || '开始处理...'}`}
                            {event.type === 'streaming' && `📊 接收到解题数据`}
                            {event.type === 'complete' && `✅ ${event.message || '处理完成！'}`}
                            {event.type === 'error' && `❌ 错误: ${event.message}`}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* 解题步骤区域 */}
        {currentSteps.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>解题步骤</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading && currentSteps.length === 0 && (
                <div className="text-center py-8">
                  <LoadingSpinner className="mx-auto mb-4" />
                  <p className="text-gray-600">正在分析题目...</p>
                </div>
              )}

              {currentSteps.length > 0 && (
                <div className="space-y-6">
                  {currentSteps.map((step) => (
                    <div key={step.step_id} className="border-l-4 border-blue-500 pl-6 pb-4">
                      <div className="font-semibold text-lg text-gray-800 mb-3">
                        步骤 {step.step_id}:
                        <MathRenderer
                          content={step.problem || ''}
                          className="inline ml-2 text-blue-700"
                          inline={true}
                        />
                      </div>

                      <div className="mb-4">
                        <MathRenderer
                          content={step.content || ''}
                          className="text-gray-700 prose prose-sm max-w-none"
                        />
                      </div>

                      {step.lecture && (
                        <div className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg border-l-4 border-blue-200">
                          <div className="font-medium mb-2 text-blue-800">💡 解题讲解</div>
                          <div className="leading-relaxed">
                            <MathRenderer
                              content={step.lecture}
                              className="text-sm"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {isComplete && (
                    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="font-medium text-green-800">✅ 解题完成！</div>
                      <div className="text-green-700 text-sm mt-1">
                        共 {currentSteps.length} 个步骤
                      </div>
                    </div>
                  )}
                </div>
              )}

              {!isLoading && currentSteps.length === 0 && !solution && (
                <div className="text-center py-8 text-gray-500">
                  等待解题结果...
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
