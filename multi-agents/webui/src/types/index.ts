// 通用API响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 通用状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 评分步骤类型
export type ScoringStep =
  | 'word_count'
  | 'topic_analysis'
  | 'structure_analysis'
  | 'content_analysis'
  | 'language_analysis'
  | 'final_scoring'
  | 'summary';

// SSE 事件类型
export interface SSEEvent {
  type: 'start' | 'progress' | 'result' | 'streaming' | 'complete' | 'error' | 'final_result';
  step?: ScoringStep;
  timestamp?: string;
  message?: string;
  score?: number;
  grade?: string;
  word_count?: number;
  details?: any;
  content?: string;
  result?: any;
  data?: any;
}

// 数学解题相关类型
export interface MathStep {
  step_id: number
  problem?: string
  content?: string
  lecture?: string
}

export interface MathSolution {
  steps: MathStep[]
}

export interface MathRequest {
  problem: string
}

// 文章评分相关类型
export interface ArticleRequest {
  article: string
}

export interface TopicAnalysis {
  theme_clarity_score: number
  depth_score: number
  relevance_score: number
  values_score: number
  feedback: string
}

export interface StructureAnalysis {
  opening_score: number
  paragraph_score: number
  transition_score: number
  ending_score: number
  feedback: string
}

export interface ContentAnalysis {
  material_score: number
  logic_score: number
  innovation_score: number
  depth_score: number
  feedback: string
}

export interface LanguageAnalysis {
  accuracy_score: number
  fluency_score: number
  vividness_score: number
  standard_score: number
  feedback: string
}

export interface ArticleScoreResult {
  word_count: number
  word_score: number
  topic_score: number
  structure_score: number
  content_score: number
  language_score: number
  total_score: number
  max_score: number
  topic_analysis: TopicAnalysis
  structure_analysis: StructureAnalysis
  content_analysis: ContentAnalysis
  language_analysis: LanguageAnalysis
  summary: string
  deduction_details: Record<string, any>
}

// 进度信息类型
export interface ProgressInfo {
  type: 'progress' | 'result';
  step: ScoringStep;
  message?: string;
  score?: number;
  details?: any;
  currentResults?: any; // 用于实时更新结果数据
}

// 主题分析结果 - 与 demo2.py TopicAnalysis 模型一致
export interface TopicAnalysis {
  main_topics: string[];
  theme_clarity_score: number;  // 0-25
  depth_score: number;          // 0-25
  relevance_score: number;      // 0-25
  values_score: number;         // 0-25
  feedback: string;
}

// 结构分析结果 - 与 demo2.py StructureAnalysis 模型一致
export interface StructureAnalysis {
  opening_score: number;        // 0-25
  paragraph_score: number;      // 0-25
  transition_score: number;     // 0-25
  ending_score: number;         // 0-25
  feedback: string;
}

// 内容分析结果 - 与 demo2.py ContentAnalysis 模型一致
export interface ContentAnalysis {
  material_score: number;       // 0-25
  logic_score: number;          // 0-25
  innovation_score: number;     // 0-25
  depth_score: number;          // 0-25
  feedback: string;
}

// 语言分析结果 - 与 demo2.py LanguageAnalysis 模型一致
export interface LanguageAnalysis {
  accuracy_score: number;       // 0-25
  fluency_score: number;        // 0-25
  vividness_score: number;      // 0-25
  standard_score: number;       // 0-25
  feedback: string;
  errors: string[];
}

// 最终评分结果 - 与 demo2.py final_scoring_node 输出一致
export interface FinalScore {
  word_score: number;           // 0-10
  topic_score: number;          // 0-100
  structure_score: number;      // 0-100
  content_score: number;        // 0-100
  language_score: number;       // 0-100
  total_score: number;          // 0-500
  grade: string;                // 优秀/良好/中等/及格/不及格
  max_score: number;            // 500
}

// 评分状态
export interface ScoringState {
  isScoring: boolean;
  currentStep: ScoringStep | null;
  progress: number;
  wordCount: number;
  topicAnalysis: TopicAnalysis | null;
  structureAnalysis: StructureAnalysis | null;
  contentAnalysis: ContentAnalysis | null;
  languageAnalysis: LanguageAnalysis | null;
  finalScore: FinalScore | null;
  summary: string | null;
  error: string | null;
}

// 数学解题相关类型（重复定义，已在上面定义）

export interface MathStepsData {
  steps: MathStep[];
}

export interface StreamingMessage {
  type: 'start' | 'streaming' | 'complete' | 'error';
  data?: MathStepsData;
  message?: string;
  timestamp?: string;
}

export interface MathSolverState {
  isProcessing: boolean;
  currentSteps: MathStep[];
  error: string | null;
  isComplete: boolean;
}
